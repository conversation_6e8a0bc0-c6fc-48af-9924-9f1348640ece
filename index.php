<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur TEIF XML - Facturation Électronique Tunisie</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #0056b3;
            background-color: #e3f2fd;
        }
        .upload-area.dragover {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .file-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        .progress-container {
            display: none;
            margin-top: 20px;
        }
        .result-container {
            display: none;
            margin-top: 30px;
        }
        .xml-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .header-logo {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        .feature-card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .feature-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="header-logo">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-file-invoice"></i> Générateur TEIF XML</h1>
                    <p class="mb-0">Convertisseur de factures PDF vers format TEIF pour la facturation électronique tunisienne</p>
                </div>
                <div class="col-md-4 text-end">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA2MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRTUzRTNFIi8+Cjx0ZXh0IHg9IjMwIiB5PSIyNSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VE48L3RleHQ+Cjwvc3ZnPgo=" alt="Tunisia Flag" class="me-2">
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Features Section -->
        <div class="row mb-5">
            <div class="col-md-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-robot fa-3x text-primary mb-3"></i>
                        <h5>IA Avancée</h5>
                        <p>Extraction automatique des données avec Google Gemini AI</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                        <h5>Conforme TEIF v1.8.8</h5>
                        <p>Format XML conforme aux standards TTN</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-download fa-3x text-info mb-3"></i>
                        <h5>Téléchargement Direct</h5>
                        <p>Récupérez votre fichier XML instantanément</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Form -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-upload"></i> Télécharger votre facture PDF</h4>
                    </div>
                    <div class="card-body">
                        <form id="uploadForm" enctype="multipart/form-data">
                            <div class="upload-area" id="uploadArea">
                                <i class="fas fa-cloud-upload-alt fa-4x text-primary mb-3"></i>
                                <h5>Glissez-déposez votre fichier PDF ici</h5>
                                <p class="text-muted">ou cliquez pour sélectionner un fichier</p>
                                <input type="file" id="pdfFile" name="pdf_file" accept=".pdf" class="d-none" required>
                            </div>
                            
                            <div id="fileInfo" class="file-info" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file-pdf text-danger me-2"></i>
                                    <span id="fileName"></span>
                                    <span id="fileSize" class="text-muted ms-2"></span>
                                    <button type="button" class="btn btn-sm btn-outline-danger ms-auto" id="removeFile">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mt-4">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">Mode d'extraction:</label>
                                        <select class="form-select" name="extraction_mode" id="extractionMode">
                                            <option value="mock">Données de test (rapide)</option>
                                            <option value="ai">IA Gemini (extraction réelle)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Format de sortie:</label>
                                        <select class="form-select" name="output_format">
                                            <option value="xml">XML TEIF</option>
                                            <option value="both">XML + JSON</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4 text-center">
                                <button type="submit" class="btn btn-primary btn-lg" id="generateBtn" disabled>
                                    <i class="fas fa-cogs"></i> Générer le XML TEIF
                                </button>
                            </div>
                        </form>

                        <!-- Progress Bar -->
                        <div class="progress-container" id="progressContainer">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%" id="progressBar"></div>
                            </div>
                            <div class="text-center mt-2">
                                <span id="progressText">Traitement en cours...</span>
                            </div>
                        </div>

                        <!-- Results -->
                        <div class="result-container" id="resultContainer">
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle"></i> Conversion réussie!</h5>
                                <p class="mb-0">Votre fichier XML TEIF a été généré avec succès.</p>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Informations extraites:</h6>
                                    <div id="extractedInfo" class="small"></div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <button class="btn btn-success" id="downloadBtn">
                                        <i class="fas fa-download"></i> Télécharger XML
                                    </button>
                                    <button class="btn btn-outline-primary ms-2" id="previewBtn">
                                        <i class="fas fa-eye"></i> Aperçu
                                    </button>
                                </div>
                            </div>

                            <div id="xmlPreview" class="xml-preview mt-3" style="display: none;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Info Section -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> À propos du format TEIF</h5>
                    </div>
                    <div class="card-body">
                        <p>Le format TEIF (Tunisia Electronic Invoice Format) est le standard officiel pour la facturation électronique en Tunisie, géré par Tunisie TradeNet (TTN).</p>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Champs supportés:</h6>
                                <ul class="small">
                                    <li>Numéro de facture</li>
                                    <li>Date d'émission</li>
                                    <li>Informations fournisseur/client</li>
                                    <li>Articles avec TVA</li>
                                    <li>Totaux HT/TTC</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Codes TVA supportés:</h6>
                                <ul class="small">
                                    <li>I-1602: TVA (7%, 19%)</li>
                                    <li>I-1601: Droit de timbre</li>
                                    <li>I-01: ID fiscal entreprise</li>
                                    <li>I-02: CIN particulier</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-dark text-white text-center py-3 mt-5">
        <p>&copy; 2025 Générateur TEIF XML - Facturation Électronique Tunisie</p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
