// Global variables
let selectedFile = null;
let currentXmlContent = '';

// DOM elements
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('pdfFile');
const fileInfo = document.getElementById('fileInfo');
const fileName = document.getElementById('fileName');
const fileSize = document.getElementById('fileSize');
const removeFileBtn = document.getElementById('removeFile');
const generateBtn = document.getElementById('generateBtn');
const uploadForm = document.getElementById('uploadForm');
const progressContainer = document.getElementById('progressContainer');
const progressBar = document.getElementById('progressBar');
const progressText = document.getElementById('progressText');
const resultContainer = document.getElementById('resultContainer');
const downloadBtn = document.getElementById('downloadBtn');
const previewBtn = document.getElementById('previewBtn');
const xmlPreview = document.getElementById('xmlPreview');
const extractedInfo = document.getElementById('extractedInfo');

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
});

function initializeEventListeners() {
    // Upload area click
    uploadArea.addEventListener('click', () => fileInput.click());
    
    // File input change
    fileInput.addEventListener('change', handleFileSelect);
    
    // Drag and drop
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    
    // Remove file
    removeFileBtn.addEventListener('click', removeFile);
    
    // Form submission
    uploadForm.addEventListener('submit', handleFormSubmit);
    
    // Download button
    downloadBtn.addEventListener('click', downloadXml);
    
    // Preview button
    previewBtn.addEventListener('click', togglePreview);
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        validateAndSetFile(file);
    }
}

function handleDragOver(event) {
    event.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        validateAndSetFile(file);
    }
}

function validateAndSetFile(file) {
    // Validate file type
    if (file.type !== 'application/pdf') {
        showAlert('Erreur: Veuillez sélectionner un fichier PDF.', 'danger');
        return;
    }
    
    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
        showAlert('Erreur: Le fichier est trop volumineux (max 10MB).', 'danger');
        return;
    }
    
    selectedFile = file;
    displayFileInfo(file);
    generateBtn.disabled = false;
}

function displayFileInfo(file) {
    fileName.textContent = file.name;
    fileSize.textContent = `(${formatFileSize(file.size)})`;
    fileInfo.style.display = 'block';
    uploadArea.style.display = 'none';
}

function removeFile() {
    selectedFile = null;
    fileInput.value = '';
    fileInfo.style.display = 'none';
    uploadArea.style.display = 'block';
    generateBtn.disabled = true;
    hideResults();
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function handleFormSubmit(event) {
    event.preventDefault();
    
    if (!selectedFile) {
        showAlert('Veuillez sélectionner un fichier PDF.', 'warning');
        return;
    }
    
    processFile();
}

function processFile() {
    showProgress();
    
    const formData = new FormData();
    formData.append('pdf_file', selectedFile);
    formData.append('extraction_mode', document.getElementById('extractionMode').value);
    formData.append('output_format', document.querySelector('select[name="output_format"]').value);
    
    // Simulate progress
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        updateProgress(progress, getProgressMessage(progress));
    }, 500);
    
    fetch('process.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        clearInterval(progressInterval);
        updateProgress(100, 'Terminé!');
        
        setTimeout(() => {
            hideProgress();
            if (data.success) {
                showResults(data);
            } else {
                showAlert('Erreur: ' + data.error, 'danger');
            }
        }, 1000);
    })
    .catch(error => {
        clearInterval(progressInterval);
        hideProgress();
        console.error('Error:', error);
        showAlert('Erreur de connexion. Veuillez réessayer.', 'danger');
    });
}

function getProgressMessage(progress) {
    if (progress < 20) return 'Lecture du fichier PDF...';
    if (progress < 40) return 'Conversion en Base64...';
    if (progress < 60) return 'Extraction des données...';
    if (progress < 80) return 'Génération du XML TEIF...';
    if (progress < 95) return 'Finalisation...';
    return 'Terminé!';
}

function showProgress() {
    progressContainer.style.display = 'block';
    generateBtn.disabled = true;
}

function hideProgress() {
    progressContainer.style.display = 'none';
    generateBtn.disabled = false;
}

function updateProgress(percent, message) {
    progressBar.style.width = percent + '%';
    progressText.textContent = message;
}

function showResults(data) {
    currentXmlContent = data.xml_content;
    
    // Display extracted information
    let infoHtml = '<ul class="list-unstyled">';
    if (data.invoice_data) {
        infoHtml += `<li><strong>N° Facture:</strong> ${data.invoice_data['Invoice Number'] || 'N/A'}</li>`;
        infoHtml += `<li><strong>Client:</strong> ${data.invoice_data['Client Name'] || 'N/A'}</li>`;
        infoHtml += `<li><strong>Total TTC:</strong> ${data.invoice_data['Total TTC Amount'] || 'N/A'} TND</li>`;
        infoHtml += `<li><strong>Articles:</strong> ${data.invoice_data['Line Items'] ? data.invoice_data['Line Items'].length : 0}</li>`;
    }
    infoHtml += '</ul>';
    extractedInfo.innerHTML = infoHtml;
    
    resultContainer.style.display = 'block';
}

function hideResults() {
    resultContainer.style.display = 'none';
    xmlPreview.style.display = 'none';
}

function downloadXml() {
    if (!currentXmlContent) {
        showAlert('Aucun contenu XML à télécharger.', 'warning');
        return;
    }
    
    const blob = new Blob([currentXmlContent], { type: 'application/xml' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `facture_teif_${new Date().getTime()}.xml`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function togglePreview() {
    if (xmlPreview.style.display === 'none') {
        xmlPreview.style.display = 'block';
        xmlPreview.innerHTML = `<pre><code>${escapeHtml(currentXmlContent)}</code></pre>`;
        previewBtn.innerHTML = '<i class="fas fa-eye-slash"></i> Masquer';
    } else {
        xmlPreview.style.display = 'none';
        previewBtn.innerHTML = '<i class="fas fa-eye"></i> Aperçu';
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function showAlert(message, type = 'info') {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert-custom');
    existingAlerts.forEach(alert => alert.remove());
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-custom`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert after the card header
    const cardBody = document.querySelector('.card-body');
    cardBody.insertBefore(alertDiv, cardBody.firstChild);
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
