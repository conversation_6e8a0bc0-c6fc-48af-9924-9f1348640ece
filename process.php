<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Configuration
define('UPLOAD_DIR', 'uploads/');
define('OUTPUT_DIR', 'output/');
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('PYTHON_SCRIPT', 'ai_studio_code.py');

// Create directories if they don't exist
if (!file_exists(UPLOAD_DIR)) {
    mkdir(UPLOAD_DIR, 0755, true);
}
if (!file_exists(OUTPUT_DIR)) {
    mkdir(OUTPUT_DIR, 0755, true);
}

try {
    // Check if request method is POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Méthode non autorisée');
    }
    
    // Check if file was uploaded
    if (!isset($_FILES['pdf_file']) || $_FILES['pdf_file']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('Erreur lors du téléchargement du fichier');
    }
    
    $uploadedFile = $_FILES['pdf_file'];
    $extractionMode = $_POST['extraction_mode'] ?? 'mock';
    $outputFormat = $_POST['output_format'] ?? 'xml';
    
    // Validate file
    validateUploadedFile($uploadedFile);
    
    // Generate unique filename
    $timestamp = time();
    $uniqueId = uniqid();
    $originalName = pathinfo($uploadedFile['name'], PATHINFO_FILENAME);
    $uploadedFileName = $originalName . '_' . $timestamp . '_' . $uniqueId . '.pdf';
    $uploadedFilePath = UPLOAD_DIR . $uploadedFileName;
    
    // Move uploaded file
    if (!move_uploaded_file($uploadedFile['tmp_name'], $uploadedFilePath)) {
        throw new Exception('Impossible de sauvegarder le fichier');
    }
    
    // Process the file
    $result = processInvoice($uploadedFilePath, $extractionMode, $outputFormat);
    
    // Clean up uploaded file
    unlink($uploadedFilePath);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Fichier traité avec succès',
        'xml_content' => $result['xml_content'],
        'invoice_data' => $result['invoice_data'],
        'processing_time' => $result['processing_time'],
        'extraction_mode' => $extractionMode
    ]);
    
} catch (Exception $e) {
    // Return error response
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function validateUploadedFile($file) {
    // Check file size
    if ($file['size'] > MAX_FILE_SIZE) {
        throw new Exception('Le fichier est trop volumineux (max 10MB)');
    }
    
    // Check file type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    if ($mimeType !== 'application/pdf') {
        throw new Exception('Le fichier doit être un PDF');
    }
    
    // Check file extension
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if ($extension !== 'pdf') {
        throw new Exception('Extension de fichier non autorisée');
    }
}

function processInvoice($filePath, $extractionMode, $outputFormat) {
    $startTime = microtime(true);
    
    // Create a temporary Python script that processes this specific file
    $tempPythonScript = createTempPythonScript($filePath, $extractionMode);
    
    try {
        // Execute Python script
        $command = "python \"$tempPythonScript\" 2>&1";
        $output = [];
        $returnCode = 0;
        
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new Exception('Erreur lors de l\'exécution du script Python: ' . implode("\n", $output));
        }
        
        // Read the generated XML file
        $xmlFilePath = 'temp_output.xml';
        if (!file_exists($xmlFilePath)) {
            throw new Exception('Fichier XML non généré');
        }
        
        $xmlContent = file_get_contents($xmlFilePath);
        if ($xmlContent === false) {
            throw new Exception('Impossible de lire le fichier XML généré');
        }
        
        // Try to read invoice data from JSON file if it exists
        $jsonFilePath = 'temp_invoice_data.json';
        $invoiceData = null;
        if (file_exists($jsonFilePath)) {
            $jsonContent = file_get_contents($jsonFilePath);
            $invoiceData = json_decode($jsonContent, true);
        }
        
        // Clean up temporary files
        unlink($tempPythonScript);
        if (file_exists($xmlFilePath)) unlink($xmlFilePath);
        if (file_exists($jsonFilePath)) unlink($jsonFilePath);
        
        $processingTime = round((microtime(true) - $startTime) * 1000, 2);
        
        return [
            'xml_content' => $xmlContent,
            'invoice_data' => $invoiceData,
            'processing_time' => $processingTime
        ];
        
    } catch (Exception $e) {
        // Clean up on error
        if (file_exists($tempPythonScript)) unlink($tempPythonScript);
        if (file_exists('temp_output.xml')) unlink('temp_output.xml');
        if (file_exists('temp_invoice_data.json')) unlink('temp_invoice_data.json');
        throw $e;
    }
}

function createTempPythonScript($pdfFilePath, $extractionMode) {
    $tempScriptPath = 'temp_process_' . uniqid() . '.py';
    
    $pythonCode = '
import sys
import os
sys.path.append(".")

# Import our existing functions
from ai_studio_code import pdf_to_base64, extract_invoice_data_with_gemini, generate_teif_xml
import json

def main():
    pdf_path = "' . addslashes($pdfFilePath) . '"
    extraction_mode = "' . $extractionMode . '"
    
    try:
        # Convert PDF to base64
        base64_data = pdf_to_base64(pdf_path)
        if not base64_data:
            raise Exception("Failed to convert PDF to base64")
        
        # Extract invoice data
        if extraction_mode == "ai":
            invoice_data = extract_invoice_data_with_gemini(base64_data)
        else:
            # Use mock data
            invoice_data = {
                "Invoice Number": "FV/2025/0007",
                "Issue Date": "06/02/2025",
                "Supplier Fiscal ID": "1327372W",
                "Supplier Name": "SOLAR ENERGY SOLUTIONS",
                "Supplier Address": "Zone Industrielle, Sfax, Tunisia",
                "Client Fiscal ID": "08777647",
                "Client Name": "EMNA KACEM",
                "Client Address": "RTE SOKRA KM 5, Sfax Sud, Tunisia",
                "Line Items": [
                    {
                        "ItemCode": "**********",
                        "Description": "MODULE PHOTOVOLTAIQUE JA SOLAR MONO 565 Wc",
                        "Quantity": 6.0,
                        "UnitPrice": 540.0,
                        "TaxRate": 7.0
                    },
                    {
                        "ItemCode": "**********", 
                        "Description": "ONDULEUR SOLIS MONO 3 KW",
                        "Quantity": 1.0,
                        "UnitPrice": 1870.0,
                        "TaxRate": 7.0
                    }
                ],
                "Total HT Amount": 4332.0,
                "Total TTC Amount": 4542.88,
                "Stamp Duty": 1.0,
                "Amount in Words": "Quatre Mille Cinq Cent Quarante-Deux Dinar et Huit Cent Quatre-Vingts Millimes"
            }
        
        if not invoice_data:
            raise Exception("Failed to extract invoice data")
        
        # Generate TEIF XML
        xml_content = generate_teif_xml(invoice_data)
        
        # Save XML to temporary file
        with open("temp_output.xml", "w", encoding="utf-8") as f:
            f.write(xml_content)
        
        # Save invoice data to JSON for PHP to read
        with open("temp_invoice_data.json", "w", encoding="utf-8") as f:
            json.dump(invoice_data, f, ensure_ascii=False, indent=2)
        
        print("SUCCESS: XML generated successfully")
        
    except Exception as e:
        print(f"ERROR: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
';
    
    file_put_contents($tempScriptPath, $pythonCode);
    return $tempScriptPath;
}

// Helper function to log errors (optional)
function logError($message) {
    $logFile = 'error.log';
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}
?>
