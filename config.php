<?php
/**
 * Configuration file for TEIF XML Generator
 */

// Load environment variables from .env file if it exists
function loadEnvFile($filePath = '.env') {
    if (!file_exists($filePath)) {
        return;
    }

    $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue; // Skip comments
        }

        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);

        if (!array_key_exists($name, $_SERVER) && !array_key_exists($name, $_ENV)) {
            putenv(sprintf('%s=%s', $name, $value));
            $_ENV[$name] = $value;
            $_SERVER[$name] = $value;
        }
    }
}

// Load .env file
loadEnvFile();

// Application settings
define('APP_NAME', 'Générateur TEIF XML');
define('APP_VERSION', '1.0.0');
define('APP_DESCRIPTION', 'Convertisseur de factures PDF vers format TEIF pour la facturation électronique tunisienne');

// File upload settings
define('MAX_UPLOAD_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_EXTENSIONS', ['pdf']);
define('UPLOAD_DIRECTORY', 'uploads/');
define('OUTPUT_DIRECTORY', 'output/');

// Python script settings
define('PYTHON_EXECUTABLE', 'python'); // or 'python3' depending on your system
define('PYTHON_SCRIPT_PATH', 'ai_studio_code.py');

// AI API settings (optional - can be set via environment variables)
// Method 1: Environment variable (recommended)
define('GEMINI_API_KEY', getenv('GEMINI_API_KEY') ?: '');

// Method 2: Direct configuration (uncomment and replace with your key)
// define('GEMINI_API_KEY', 'your_actual_api_key_here');

define('ENABLE_AI_EXTRACTION', !empty(GEMINI_API_KEY));

// TEIF settings
define('TEIF_VERSION', '1.8.8');
define('CONTROLLING_AGENCY', 'TTN');

// Security settings
define('ENABLE_CSRF_PROTECTION', true);
define('SESSION_TIMEOUT', 3600); // 1 hour

// Logging settings
define('ENABLE_LOGGING', true);
define('LOG_FILE', 'logs/app.log');
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR

// Cache settings
define('ENABLE_CACHE', false);
define('CACHE_DIRECTORY', 'cache/');
define('CACHE_LIFETIME', 3600); // 1 hour

// Database settings (if needed for future features)
define('DB_HOST', 'localhost');
define('DB_NAME', 'teif_generator');
define('DB_USER', 'root');
define('DB_PASS', '');

// Email settings (for notifications)
define('SMTP_HOST', '');
define('SMTP_PORT', 587);
define('SMTP_USER', '');
define('SMTP_PASS', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'TEIF Generator');

// Feature flags
define('FEATURE_BATCH_PROCESSING', false);
define('FEATURE_EMAIL_NOTIFICATIONS', false);
define('FEATURE_USER_ACCOUNTS', false);
define('FEATURE_INVOICE_HISTORY', false);

// Supported languages
define('SUPPORTED_LANGUAGES', ['fr', 'ar', 'en']);
define('DEFAULT_LANGUAGE', 'fr');

// Tunisia-specific settings
define('TUNISIA_COUNTRY_CODE', 'TN');
define('TUNISIA_CURRENCY', 'TND');
define('TUNISIA_VAT_RATES', [0, 7, 19]);

// Tax type codes
define('TAX_CODES', [
    'VAT' => 'I-1602',
    'STAMP_DUTY' => 'I-1601',
    'COMPANY_ID' => 'I-01',
    'INDIVIDUAL_ID' => 'I-02'
]);

// Document type codes
define('DOCUMENT_TYPES', [
    'INVOICE' => 'I-11',
    'CREDIT_NOTE' => 'I-12',
    'DEBIT_NOTE' => 'I-13'
]);

// Partner function codes
define('PARTNER_FUNCTIONS', [
    'SUPPLIER' => 'I-66',
    'BUYER' => 'I-64',
    'PAYER' => 'I-62'
]);

// Amount type codes
define('AMOUNT_TYPES', [
    'LINE_TOTAL' => 'I-171',
    'TOTAL_HT' => 'I-176',
    'TAX_BASE' => 'I-177',
    'TAX_AMOUNT' => 'I-178',
    'TOTAL_TTC' => 'I-180'
]);

// Date function codes
define('DATE_FUNCTIONS', [
    'ISSUE_DATE' => 'I-31',
    'DUE_DATE' => 'I-32',
    'SERVICE_PERIOD' => 'I-36'
]);

// Utility functions
function getConfigValue($key, $default = null) {
    return defined($key) ? constant($key) : $default;
}

function isFeatureEnabled($feature) {
    return getConfigValue($feature, false) === true;
}

function getUploadPath($filename = '') {
    $path = rtrim(UPLOAD_DIRECTORY, '/') . '/';
    if (!file_exists($path)) {
        mkdir($path, 0755, true);
    }
    return $path . $filename;
}

function getOutputPath($filename = '') {
    $path = rtrim(OUTPUT_DIRECTORY, '/') . '/';
    if (!file_exists($path)) {
        mkdir($path, 0755, true);
    }
    return $path . $filename;
}

function getLogPath() {
    $logDir = dirname(LOG_FILE);
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    return LOG_FILE;
}

function logMessage($level, $message, $context = []) {
    if (!ENABLE_LOGGING) return;
    
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' ' . json_encode($context) : '';
    $logEntry = "[$timestamp] [$level] $message$contextStr\n";
    
    file_put_contents(getLogPath(), $logEntry, FILE_APPEND | LOCK_EX);
}

// Initialize application
function initializeApp() {
    // Start session if needed
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Set timezone
    date_default_timezone_set('Africa/Tunis');
    
    // Create necessary directories
    getUploadPath();
    getOutputPath();
    
    if (ENABLE_LOGGING) {
        getLogPath();
    }
    
    // Log application start
    logMessage('INFO', 'Application initialized', [
        'version' => APP_VERSION,
        'ai_enabled' => ENABLE_AI_EXTRACTION
    ]);
}

// Error handler
function handleError($errno, $errstr, $errfile, $errline) {
    $errorTypes = [
        E_ERROR => 'ERROR',
        E_WARNING => 'WARNING',
        E_NOTICE => 'NOTICE',
        E_USER_ERROR => 'USER_ERROR',
        E_USER_WARNING => 'USER_WARNING',
        E_USER_NOTICE => 'USER_NOTICE'
    ];
    
    $type = $errorTypes[$errno] ?? 'UNKNOWN';
    $message = "PHP $type: $errstr in $errfile on line $errline";
    
    logMessage('ERROR', $message);
    
    // Don't execute PHP internal error handler
    return true;
}

// Set error handler
if (ENABLE_LOGGING) {
    set_error_handler('handleError');
}

// Initialize the application
initializeApp();
?>
