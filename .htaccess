# TEIF XML Generator - Apache Configuration

# Enable rewrite engine
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (adjust as needed)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.jsdelivr.net cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' cdn.jsdelivr.net cdnjs.cloudflare.com; font-src 'self' cdnjs.cloudflare.com; img-src 'self' data:; connect-src 'self';"
</IfModule>

# File Upload Security
<Files "*.php">
    <IfModule mod_php.c>
        php_flag file_uploads On
        php_value upload_max_filesize 10M
        php_value post_max_size 12M
        php_value max_execution_time 300
        php_value memory_limit 256M
    </IfModule>
</Files>

# Deny access to sensitive files
<FilesMatch "\.(log|json|py|pyc|pyo|txt|md|yml|yaml|ini|conf)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Deny access to directories
<DirectoryMatch "(uploads|output|logs|cache|temp)">
    Options -Indexes
    <FilesMatch "\.(pdf|xml|json)$">
        Order Allow,Deny
        Deny from all
    </FilesMatch>
</DirectoryMatch>

# Deny access to Python files
<FilesMatch "\.py$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Deny access to configuration files
<FilesMatch "(config|\.env|\.htaccess|\.htpasswd)">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
</IfModule>

# Error pages (optional)
ErrorDocument 404 /404.html
ErrorDocument 500 /500.html

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Limit file upload size at server level
LimitRequestBody 10485760

# Custom error handling for PHP
<IfModule mod_php.c>
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_errors.log
</IfModule>

# Redirect HTTP to HTTPS (uncomment if using SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Pretty URLs (optional)
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule ^([^/]+)/?$ index.php?page=$1 [L,QSA]

# Prevent hotlinking (optional)
# RewriteCond %{HTTP_REFERER} !^$
# RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?yourdomain.com [NC]
# RewriteRule \.(jpg|jpeg|png|gif|pdf|xml)$ - [NC,F,L]
