import requests
import xml.etree.ElementTree as ET
from xml.dom import minidom
from datetime import datetime
import base64
# Removed cryptography imports as they are no longer needed
# from cryptography.hazmat.primitives import hashes
# from cryptography.hazmat.primitives.asymmetric import padding
# from cryptography.hazmat.primitives import serialization
# from cryptography.hazmat.backends import default_backend
# import os # For handling private keys, ideally from environment variables or a secure store

# --- Configuration ---
# Replace with your actual API key and endpoint for Gemini
GEMINI_API_KEY = "AIzaSyCvvzWhEC0ibjDtx5orS-OAVdn5DLJT5eM"
GEMINI_API_ENDPOINT = "https://generativelanguage.googleapis.com" # e.g., "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent"

# XML Namespace for digital signature (no longer explicitly needed for the TEIF root, but kept for clarity if other parts use it)
# DS_NAMESPACE = "http://www.w3.org/2000/09/xmldsig#"
# XADES_NAMESPACE = "http://uri.etsi.org/01903/v1.3.2#"

# --- Part 1a: PDF to Base64 (for AI API) ---
def pdf_to_base64(pdf_path):
    """Reads a PDF file and encodes it to a base64 string."""
    try:
        with open(pdf_path, "rb") as pdf_file:
            encoded_string = base64.b64encode(pdf_file.read()).decode('utf-8')
        return encoded_string
    except FileNotFoundError:
        print(f"Error: PDF file not found at {pdf_path}")
        return None
    except Exception as e:
        print(f"Error encoding PDF: {e}")
        return None

# --- Part 1b: Send to AI API and Get Data ---
def extract_invoice_data_with_gemini(base64_pdf_data):
    """
    Sends the base64 encoded PDF to a Gemini-like API for data extraction.
    Returns a dictionary of extracted invoice data.
    """
    headers = {
        "Content-Type": "application/json",
    }
    # Adjust this prompt based on the specific structure your AI expects
    # and the level of detail you want from the extraction.
    prompt = f"""
    Analyze the following base64 encoded PDF invoice and extract the following mandatory fields.
    Return the data as a JSON object with keys exactly matching the "Data Field to Extract" from the provided table.
    For line items, return a list of dictionaries, each with 'Description', 'Quantity', 'UnitPrice', 'TaxRate'.
    
    Data Fields to Extract:
    - Invoice Number (Facture N°)
    - Issue Date (Date) (Format: YYYY-MM-DD)
    - Supplier Fiscal ID (Matricule)
    - Supplier Name
    - Supplier Address
    - Client Fiscal ID
    - Client Name
    - Client Address
    - Line Items (Description, Quantity, UnitPrice, TaxRate)
    - Total HT Amount
    - Total TTC Amount
    - Tax Rates/Type (e.g., "TVA 0%", "TVA 19%")
    - Stamp Duty (Droit de Timbre)
    - Amount in Words
    
    PDF Content (base64): {base64_pdf_data}
    """
    
    # Example structure for Gemini API request (you'll need to adapt this)
    data = {
        "contents": [
            {
                "parts": [
                    {"text": prompt}
                    # For actual PDF processing, you might need to send the PDF as a blob or use a vision model
                    # If Gemini has a direct PDF input, it would be structured differently.
                    # For now, we assume a text-based description or a prompt that refers to an already processed PDF.
                ]
            }
        ],
        "generationConfig": {
            "temperature": 0.1, # Lower temperature for factual extraction
            "topK": 1,
            "topP": 1,
            "maxOutputTokens": 2048,
        }
    }

    try:
        response = requests.post(
            f"{GEMINI_API_ENDPOINT}?key={GEMINI_API_KEY}",
            json=data,
            headers=headers
        )
        response.raise_for_status() # Raise an HTTPError for bad responses (4xx or 5xx)
        
        response_json = response.json()
        
        # Parse the AI's response. This is highly dependent on how your AI API returns data.
        # Assuming the AI returns a JSON string within its text response.
        ai_output_text = response_json["candidates"][0]["content"]["parts"][0]["text"]
        
        # Attempt to parse the AI's text output as JSON
        extracted_data = json.loads(ai_output_text)
        print("AI extracted data:", extracted_data)
        return extracted_data
        
    except requests.exceptions.HTTPError as errh:
        print(f"Http Error: {errh}")
    except requests.exceptions.ConnectionError as errc:
        print(f"Error Connecting: {errc}")
    except requests.exceptions.Timeout as errt:
        print(f"Timeout Error: {errt}")
    except requests.exceptions.RequestException as err:
        print(f"Something Else: {err}")
    except json.JSONDecodeError as e:
        print(f"Error decoding AI response as JSON: {e}")
        print("AI Raw Output:", ai_output_text)
    except IndexError:
        print("AI response format unexpected. No candidates or parts found.")
    return None

# --- Part 1c: Generate TEIF XML ---

# Helper function for XML formatting
def _prettify(elem):
    """Return a pretty-printed XML string for the Element."""
    rough_string = ET.tostring(elem, 'utf-8')
    reparsed = minidom.parseString(rough_string)
    return reparsed.toprettyxml(indent="  ")

def generate_teif_xml(invoice_data): # Removed private_key_path and certificate_path parameters
    """
    Generates the TEIF XML structure from extracted invoice data.
    The electronic signature block is NOT included in this version.
    """
    
    # 1. Root Element
    # No need to register ds and xades namespaces if signature is removed
    teif = ET.Element("TEIF", version="2.0", controlingAgency="TTN")

    # 2. Invoice Header
    header = ET.SubElement(teif, "InvoiceHeader")
    ET.SubElement(header, "MessageSenderIdentifier", type="I-01").text = invoice_data["Supplier Fiscal ID"]
    ET.SubElement(header, "MessageRecieverIdentifier", type="I-01").text = invoice_data.get("Client Fiscal ID", "999999999ABC")

    # 3. Invoice Body
    body = ET.SubElement(teif, "InvoiceBody")

    # Bgm (Beginning Message)
    bgm = ET.SubElement(body, "Bgm")
    ET.SubElement(bgm, "DocumentIdentifier").text = invoice_data["Invoice Number"]
    ET.SubElement(bgm, "DocumentType", Code="I-11")

    # Dtm (Date/Period)
    dtm = ET.SubElement(body, "Dtm")
    ET.SubElement(dtm, "DateText", functionCode="I-31").text = invoice_data["Issue Date"]

    # Partner Section (Supplier and Client)
    partner_section = ET.SubElement(body, "PartnerSection")

    # Supplier Details
    supplier_partner = ET.SubElement(partner_section, "Partner", functionCode="I-62")
    supplier_nad = ET.SubElement(supplier_partner, "Nad")
    ET.SubElement(supplier_nad, "PartnerIdentifier", type="I-01").text = invoice_data["Supplier Fiscal ID"]
    ET.SubElement(supplier_nad, "PartnerNom").text = invoice_data["Supplier Name"]
    supplier_address = ET.SubElement(supplier_nad, "PartnerAdresses")
    # ET.SubElement(supplier_address, "Street").text = invoice_data["Supplier Address"].get("Street", "")
    # ET.SubElement(supplier_address, "CityName").text = invoice_data["Supplier Address"].get("City", "")

    # Client Details
    client_partner = ET.SubElement(partner_section, "Partner", functionCode="I-61")
    client_nad = ET.SubElement(client_partner, "Nad")
    ET.SubElement(client_nad, "PartnerIdentifier", type="I-01").text = invoice_data["Client Fiscal ID"]
    ET.SubElement(client_nad, "PartnerNom").text = invoice_data["Client Name"]
    client_address = ET.SubElement(client_nad, "PartnerAdresses")
    # ET.SubElement(client_address, "Street").text = invoice_data["Client Address"].get("Street", "")
    # ET.SubElement(client_address, "CityName").text = invoice_data["Client Address"].get("City", "")

    # Line Item Section
    lin_section = ET.SubElement(body, "LinSection")
    for i, item in enumerate(invoice_data["Line Items"]):
        lin = ET.SubElement(lin_section, "Lin", sequenceNumber=str(i + 1))
        ET.SubElement(lin, "LinImd").text = item["Description"]
        ET.SubElement(lin, "LinQty", measurementUnit="H87").text = str(item["Quantity"]) # H87 = Unit
        
        lin_tax = ET.SubElement(lin, "LinTax", taxRate=str(item["TaxRate"]))
        ET.SubElement(lin_tax, "LinTaxType", Code="I-1602") # Assuming TVA

        lin_moa = ET.SubElement(lin, "LinMoa")
        ET.SubElement(lin_moa, "Amount", amountTypeCode="I-171", currencyIdentifier="TND").text = f"{item['Quantity'] * item['UnitPrice']:.3f}".replace('.', ',')

    # Invoice Totals and Taxes
    invoice_moa = ET.SubElement(body, "InvoiceMoa")
    
    # Total HT
    amount_details_ht = ET.SubElement(invoice_moa, "AmountDetails")
    ET.SubElement(amount_details_ht, "Moa", amountTypeCode="I-176", currencyIdentifier="TND").text = f"{invoice_data['Total HT Amount']:.3f}".replace('.', ',')
    
    # Total TTC
    amount_details_ttc = ET.SubElement(invoice_moa, "AmountDetails")
    ET.SubElement(amount_details_ttc, "Moa", amountTypeCode="I-180", currencyIdentifier="TND").text = f"{invoice_data['Total TTC Amount']:.3f}".replace('.', ',')

    # Amount in Words (Conditional)
    if "Amount in Words" in invoice_data:
        moa_type_words = ET.SubElement(invoice_moa, "MoaType")
        ET.SubElement(moa_type_words, "AmountDescription").text = invoice_data["Amount in Words"]

    invoice_tax = ET.SubElement(body, "InvoiceTax")
    
    # Stamp Duty
    stamp_tax_details = ET.SubElement(invoice_tax, "TaxDetails")
    ET.SubElement(stamp_tax_details, "TaxType", Code="I-1601") # Droit de timbre
    ET.SubElement(stamp_tax_details, "TaxAmount", currencyIdentifier="TND").text = f"{invoice_data['Stamp Duty']:.3f}".replace('.', ',')

    # TVA
    total_tva = invoice_data['Total TTC Amount'] - invoice_data['Total HT Amount'] - invoice_data['Stamp Duty']
    tva_tax_details = ET.SubElement(invoice_tax, "TaxDetails")
    ET.SubElement(tva_tax_details, "TaxType", Code="I-1602") # TVA
    ET.SubElement(tva_tax_details, "TaxAmount", currencyIdentifier="TND").text = f"{total_tva:.3f}".replace('.', ',')
    
    # --- NO XML Canonicalization or Signing ---
    # All signature-related code has been removed.

    return _prettify(teif)


# --- Main Execution Flow ---
if __name__ == "__main__":
    import json # For handling AI response

    invoice_pdf_path = "FV_2025_0007.pdf" # Make sure you have an invoice.pdf in the same directory

    # Step 1: Convert PDF to Base64
    base64_data = pdf_to_base64(invoice_pdf_path)
    if base64_data:
        print("PDF converted to Base64. Sending to AI for extraction...")
        
        # --- MOCK AI RESPONSE FOR TESTING ---
        extracted_invoice_data = {
            "Invoice Number": "FACTU/2025/12/0014",
            "Issue Date": "2025-12-01",
            "Supplier Fiscal ID": "0513287HPM000",
            "Supplier Name": "SUPPLIER S.A.R.L.",
            "Supplier Address": {"Street": "123 Main St", "City": "Tunis", "PostalCode": "1001"},
            "Client Fiscal ID": "123456789ABC",
            "Client Name": "CLIENT LTD",
            "Client Address": {"Street": "456 Oak Ave", "City": "Sfax", "PostalCode": "3000"},
            "Line Items": [
                {"Description": "LOYER", "Quantity": 1.000, "UnitPrice": 735.000, "TaxRate": "0"},
            ],
            "Total HT Amount": 735.000,
            "Total TTC Amount": 735.000,
            "Tax Rates/Type": "TVA 0%",
            "Stamp Duty": 0.500,
            "Amount in Words": "Sept Cent Trente-Cinq Dinars Cinq Cent Millimes"
        }
        print("Invoice data extracted successfully (using mock data).")
        # --- END MOCK AI RESPONSE ---
        
        # If you want to use the actual AI call:
        # extracted_invoice_data = extract_invoice_data_with_gemini(base64_data)


        if extracted_invoice_data:
            print("\nGenerating TEIF XML without signature...")
            # Step 3: Generate TEIF XML (without signature parameters)
            teif_xml_output = generate_teif_xml(extracted_invoice_data)
            
            if teif_xml_output:
                with open("output_invoice_unsigned.xml", "w", encoding="utf-8") as f:
                    f.write(teif_xml_output)
                print("\nTEIF XML (unsigned) generated and saved to output_invoice_unsigned.xml")
                print("\nGenerated XML (first 500 chars):")
                print(teif_xml_output[:500])
                
                print("\nHere's a representation of your generated XML structure, now without the digital signature block:")
                
            else:
                print("Failed to generate TEIF XML.")
        else:
            print("Failed to extract invoice data from PDF.")
    else:
        print("Failed to convert PDF to Base64.")